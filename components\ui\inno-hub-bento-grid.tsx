import React from "react";
import { motion } from "framer-motion";
import { AnimatedGradient } from "@/components/ui/animated-gradient-with-svg";
import {
  Lightbulb,
  Users,
  TrendingUp,
  Rocket,
  Building,
  Zap
} from "lucide-react";

interface BentoCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  colors: string[];
  delay: number;
  icon?: React.ReactNode;
  size?: "small" | "medium" | "large";
}

const BentoCard: React.FC<BentoCardProps> = ({
  title,
  value,
  subtitle,
  colors,
  delay,
  icon,
  size = "medium",
}) => {
  
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: delay + 0.3,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  const sizeClasses = {
    small: "h-40 sm:h-48 md:h-52",
    medium: "h-48 sm:h-56 md:h-64",
    large: "h-56 sm:h-64 md:h-72",
    xl: "h-64 sm:h-72 md:h-80"
  };

  return (
    <motion.div
      className={`relative overflow-hidden ${sizeClasses[size]} bg-black/40 dark:bg-black/60 border border-purple-500/20 rounded-2xl group hover:border-purple-400/40 transition-all duration-300`}
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      whileHover={{ y: -8, scale: 1.02 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true }}
    >
      <AnimatedGradient colors={colors} speed={0.05} blur="medium" />
      <motion.div
        className="relative z-10 p-4 sm:p-6 md:p-8 text-white backdrop-blur-sm h-full flex flex-col justify-between"
        variants={container}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true }}
      >
        <div>
          {icon && (
            <motion.div 
              className="mb-4 text-purple-400"
              variants={item}
            >
              {icon}
            </motion.div>
          )}
          <motion.h3 
            className="text-sm sm:text-base md:text-lg text-white/90 mb-2" 
            variants={item}
          >
            {title}
          </motion.h3>
          <motion.p
            className="text-xl sm:text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent"
            variants={item}
          >
            {value}
          </motion.p>
        </div>
        {subtitle && (
          <motion.p 
            className="text-xs sm:text-sm text-white/70 leading-relaxed" 
            variants={item}
          >
            {subtitle}
          </motion.p>
        )}
      </motion.div>
      
      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />
    </motion.div>
  );
};

const InnoHubBentoGrid: React.FC = () => {

  return (
    <section className="py-16 sm:py-24 lg:py-32 bg-black text-white relative overflow-hidden">
      {/* Enhanced Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-950/5 to-black"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(147,51,234,0.08),transparent_70%)]"></div>

      {/* Floating cosmic elements */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 8 }).map((_, i) => (
          <motion.div
            key={`cosmic-${i}`}
            className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
            style={{
              left: `${10 + i * 12}%`,
              top: `${15 + (i % 4) * 20}%`,
            }}
            animate={{
              opacity: [0.2, 0.6, 0.2],
              scale: [0.8, 1.4, 0.8],
              y: [-20, 20, -20],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              delay: i * 0.8,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12 lg:mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-white via-purple-200 to-purple-400 bg-clip-text text-transparent"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Innovation by the Numbers
          </motion.h2>
          <motion.p
            className="text-lg sm:text-xl text-white/80 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Discover the impact we're making in the entrepreneurial ecosystem
          </motion.p>
        </motion.div>

        {/* Enhanced Bento Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-6 lg:grid-cols-12 gap-4 md:gap-6 lg:gap-8">
          {/* Row 1: Hero card + Two medium cards */}
          <div className="md:col-span-4 lg:col-span-6">
            <BentoCard
              title="Projects Received"
              value="130+"
              subtitle="Through 5 selection rounds with innovative solutions across multiple industries"
              colors={["#3B82F6", "#60A5FA", "#93C5FD"]}
              delay={0.2}
              icon={<Lightbulb className="h-8 w-8" />}
              size="xl"
            />
          </div>

          <div className="md:col-span-2 lg:col-span-3">
            <BentoCard
              title="Expert Mentors"
              value="50+"
              subtitle="Industry professionals guiding entrepreneurs"
              colors={["#60A5FA", "#34D399", "#93C5FD"]}
              delay={0.4}
              icon={<Users className="h-6 w-6" />}
              size="medium"
            />
          </div>

          <div className="md:col-span-2 lg:col-span-3">
            <BentoCard
              title="Success Rate"
              value="85%"
              subtitle="Companies successfully launched"
              colors={["#F59E0B", "#A78BFA", "#FCD34D"]}
              delay={0.6}
              icon={<TrendingUp className="h-6 w-6" />}
              size="medium"
            />
          </div>

          {/* Row 2: Three equal cards */}
          <div className="md:col-span-2 lg:col-span-4">
            <BentoCard
              title="Investment Recipients"
              value="2"
              subtitle="Companies received funding and strategic investment"
              colors={["#3B82F6", "#A78BFA", "#FBCFE8"]}
              delay={0.8}
              icon={<Rocket className="h-6 w-6" />}
              size="medium"
            />
          </div>

          <div className="md:col-span-2 lg:col-span-4">
            <BentoCard
              title="IT Projects"
              value="31%"
              subtitle="Information Technology sector leading innovation"
              colors={["#EC4899", "#F472B6", "#3B82F6"]}
              delay={1.0}
              icon={<Building className="h-6 w-6" />}
              size="medium"
            />
          </div>

          <div className="md:col-span-2 lg:col-span-4">
            <BentoCard
              title="Innovation Programs"
              value="5"
              subtitle="Specialized tracks for different industries and stages"
              colors={["#8B5CF6", "#A78BFA", "#C084FC"]}
              delay={1.2}
              icon={<Zap className="h-6 w-6" />}
              size="medium"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export { InnoHubBentoGrid };
