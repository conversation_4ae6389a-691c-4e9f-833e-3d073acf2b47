{"name": "innohub", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@21st-extension/react": "^0.5.14", "@21st-extension/toolbar-next": "^0.5.14", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.11.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@react-email/components": "^0.3.1", "@splinetool/react-spline": "^4.0.0", "@studio-freight/lenis": "^1.0.42", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "@tabler/icons-react": "^3.31.0", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.3", "gsap": "^3.13.0", "lucide-react": "^0.508.0", "next": "15.3.2", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "ogl": "^1.0.11", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-email": "^4.1.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}