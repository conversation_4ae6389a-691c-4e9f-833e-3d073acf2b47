'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { <PERSON>kehBackground, GlassCard } from '@/components/ui/aceternity/bokeh-background';
import { Button } from '@/components/ui/button';
import { ArrowRight, TrendingUp, PieChart, BarChart, Lightbulb, LineChart, Users } from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';


// Import our custom components
import { ParallaxHero } from '@/components/investors/ParallaxHero';
import { InvestorCompanyCard } from '@/components/investors/InvestorCompanyCard';
import { PartnerCard } from '@/components/investors/PartnerCard';
import { HorizontalScroll, HorizontalScrollItem } from '@/components/investors/HorizontalScroll';
import { ContactSection } from '@/components/investors/ContactSection';
import { CursorEffect } from '@/components/investors/CursorEffect';
import TextHoverEffectDemo from '@/components/ui/text-hover-effect-demo';


// Import data
import { investorCompanies, partners } from '@/lib/data/investors';

// Define portfolioHighlights data
const portfolioHighlights = [
  {
    name: 'TechNova',
    industry: 'FinTech',
    investment: 'Seed',
    year: 2020,
    status: 'Acquired',
    return: '8.5x',
  },
  {
    name: 'GreenSolutions',
    industry: 'CleanTech',
    investment: 'Series A',
    year: 2021,
    status: 'Growing',
    return: 'Projected 4x',
  },
  {
    name: 'HealthAI',
    industry: 'HealthTech',
    investment: 'Seed',
    year: 2019,
    status: 'Series B',
    return: 'Projected 6x',
  },
  {
    name: 'DataSphere',
    industry: 'AI/ML',
    investment: 'Series A',
    year: 2022,
    status: 'Growing',
    return: 'Projected 3.5x',
  },
];



// Legacy data for sections we're keeping
const investmentOpportunities = [
  {
    title: 'Seed Funding',
    description: 'Support early-stage startups with high growth potential.',
    minInvestment: '$25,000',
    expectedReturn: '5-10x',
    timeline: '5-7 years',
  },
  {
    title: 'Series A',
    description: 'Invest in proven startups ready to scale their operations.',
    minInvestment: '$100,000',
    expectedReturn: '3-5x',
    timeline: '4-6 years',
  },
  {
    title: 'Growth Fund',
    description: 'Participate in later-stage funding rounds for established startups.',
    minInvestment: '$250,000',
    expectedReturn: '2-3x',
    timeline: '3-5 years',
  },
];

export default function InvestorsPage() {
  const { t } = useLanguage();

  return (
    <>
      {/* Background Text Effect - Footer Area Only */}
      <div className="fixed bottom-0 left-0 right-0 h-[600px] z-[-10] flex items-center justify-center pointer-events-none">
        <div className="pointer-events-auto">
          <TextHoverEffectDemo />
        </div>
      </div>

      {/* Custom cursor effect */}
      <CursorEffect />

      {/* Hero section with 3D parallax effect */}
      <ParallaxHero />

      {/* Investment Opportunities Section - Keeping this section from the original */}
      <section className="py-24 relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-background to-background z-0" />

        {/* Using BokehBackground component instead of custom implementation */}
        <div className="absolute inset-0 z-0">
          <BokehBackground
            className="w-full h-full"
            density={15}
            speed={1.5}
            colors={['#ffcc33', '#ffaa00', '#ff8800', '#00aaff']}
          >
            <div className="hidden">Content placeholder</div>
          </BokehBackground>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <TextReveal className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80">
              {t('investors.investmentOpportunities')}
            </h2>
            <p className="text-xl text-white/70">
              {t('investors.discoverInvestment')}
            </p>
          </TextReveal>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            {investmentOpportunities.map((opportunity, index) => (
              <motion.div
                key={opportunity.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
              >
                <GlassCard className="h-full p-1">
                  <div className="p-6 h-full flex flex-col">
                    <div className="mb-6">
                      <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mb-4">
                        {(() => {
                          if (index === 0) return <Lightbulb className="h-6 w-6 text-primary" />;
                          if (index === 1) return <LineChart className="h-6 w-6 text-primary" />;
                          return <BarChart className="h-6 w-6 text-primary" />;
                        })()}
                      </div>
                      <h3 className="text-2xl font-bold mb-3 text-white">{opportunity.title}</h3>
                      <p className="text-white/70 mb-6">{opportunity.description}</p>
                    </div>

                    <div className="space-y-4 mb-8 flex-grow">
                      <div className="flex justify-between items-center border-b border-white/10 pb-2">
                        <span className="text-sm text-white/60">Minimum Investment</span>
                        <span className="font-medium text-white">{opportunity.minInvestment}</span>
                      </div>
                      <div className="flex justify-between items-center border-b border-white/10 pb-2">
                        <span className="text-sm text-white/60">Expected Return</span>
                        <span className="font-medium text-primary">{opportunity.expectedReturn}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-white/60">Timeline</span>
                        <span className="font-medium text-white">{opportunity.timeline}</span>
                      </div>
                    </div>

                    <div className="mt-auto">
                      <Button variant="outline" className="w-full border-white/20 text-white hover:bg-white/10" asChild>
                        <Link href="/investors/apply">
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Professional Investor Companies Section */}
      <section id="investor-companies" className="py-32 relative overflow-hidden">
        {/* Clean Professional Background */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-gray-950/30 to-black z-0" />

        {/* Subtle Professional BokehBackground */}
        <BokehBackground
          className="absolute inset-0 z-[1]"
          density={20}
          speed={0.8}
          colors={['#6366f1', '#8b5cf6', '#a855f7']}
        >
          <div className="hidden">Background effect</div>
        </BokehBackground>

        {/* Professional gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-transparent to-black/40 z-[2]" />

        {/* Minimal grid pattern */}
        <div className="absolute inset-0 bg-grid-white/[0.01] bg-[length:100px_100px] z-[3]" />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Professional Header */}
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
            viewport={{ once: true }}
          >
            {/* Professional divider */}
            <motion.div
              className="inline-block mb-6"
              initial={{ width: 0 }}
              whileInView={{ width: 80 }}
              transition={{ duration: 1, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="inline-block rounded-lg bg-white/5 px-6 py-2 text-sm font-medium text-white/80 mb-8 backdrop-blur-sm border border-white/10"
            >
              Investment Partners
            </motion.div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 tracking-tight leading-tight">
              <motion.span
                className="block text-white"
                initial={{ opacity: 0, y: 15 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                Our Investor Companies
              </motion.span>
            </h2>

            <motion.p
              className="text-lg md:text-xl text-white/60 leading-relaxed max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
            >
              Partnering with leading investment firms and venture capital companies to drive innovation and growth in the startup ecosystem.
            </motion.p>
          </motion.div>

          {/* Professional Company Showcase */}
          <div className="relative">
            {/* Clean Grid Layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
              {investorCompanies.map((company, index) => (
                <motion.div
                  key={company.id}
                  className="group relative"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.6,
                    delay: index * 0.05,
                    ease: [0.22, 1, 0.36, 1]
                  }}
                  viewport={{ once: true, margin: "-30px" }}
                  whileHover={{
                    y: -8,
                    transition: { duration: 0.3, ease: "easeOut" }
                  }}
                >
                  {/* Subtle Card Background Effect */}
                  <div className="absolute -inset-2 bg-gradient-to-br from-white/5 to-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"></div>

                  <InvestorCompanyCard
                    name={company.name}
                    logo={company.logo}
                    description={company.description}
                    investmentFocus={company.investmentFocus}
                    website={company.website}
                    featured={company.featured}
                    index={index}
                  />

                  {/* Professional Hover Effect */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-white/5 via-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                </motion.div>
              ))}
            </div>

          </div>

          {/* Professional Summary Stats */}
          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center gap-8 bg-white/5 backdrop-blur-sm rounded-2xl px-8 py-4 border border-white/10">
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">{investorCompanies.length}+</div>
                <div className="text-xs text-white/60 uppercase tracking-wider">Partners</div>
              </div>
              <div className="w-px h-8 bg-white/20"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">$120M+</div>
                <div className="text-xs text-white/60 uppercase tracking-wider">Invested</div>
              </div>
              <div className="w-px h-8 bg-white/20"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">45+</div>
                <div className="text-xs text-white/60 uppercase tracking-wider">Portfolio</div>
              </div>
            </div>
          </motion.div>

          {/* Professional Call to Action */}
          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                asChild
                className="bg-white text-black hover:bg-white/90 px-8 py-3 font-medium"
              >
                <Link href="#contact">
                  Partner With Us
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                asChild
                className="border-white/30 text-white hover:bg-white/10 px-8 py-3 font-medium"
              >
                <Link href="/investors/portfolio">
                  View Portfolio
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>



      {/* Why Partner With Us Section */}
      <section className="py-24 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-background to-black z-0" />
        <div className="absolute inset-0 bg-[url('/images/investors/lightbulb-plant.jpg')] bg-cover bg-center opacity-5 z-0" />

        <div className="container mx-auto px-4 relative z-10">
          <TextReveal className="text-center max-w-3xl mx-auto mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80">
              {t('investors.whyPartner')}
            </h2>
            <p className="text-xl text-white/70">
              {t('investors.joinCommunity')}
            </p>
          </TextReveal>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            <div className="space-y-10">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7 }}
                viewport={{ once: true }}
                className="relative"
              >
                <GlassCard className="p-6 overflow-visible">
                  <div className="absolute -left-5 -top-5 w-16 h-16 rounded-2xl bg-primary/20 backdrop-blur-md flex items-center justify-center border border-primary/30">
                    <TrendingUp className="h-8 w-8 text-primary" />
                  </div>
                  <div className="pl-8">
                    <h3 className="text-2xl font-bold mb-4 text-white">Exceptional Track Record</h3>
                    <p className="text-white/70 text-lg">
                      Our portfolio has consistently outperformed market benchmarks, with an average return of <span className="text-primary font-semibold">4.7x</span> across all investments.
                    </p>
                    <ul className="mt-4 space-y-2">
                      <li className="flex items-center text-white/70">
                        <div className="w-2 h-2 rounded-full bg-primary mr-2" />
                        <span>85% success rate for seed-stage investments</span>
                      </li>
                      <li className="flex items-center text-white/70">
                        <div className="w-2 h-2 rounded-full bg-primary mr-2" />
                        <span>12 successful exits in the last 3 years</span>
                      </li>
                    </ul>
                  </div>
                </GlassCard>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
                viewport={{ once: true }}
                className="relative"
              >
                <GlassCard className="p-6 overflow-visible">
                  <div className="absolute -left-5 -top-5 w-16 h-16 rounded-2xl bg-primary/20 backdrop-blur-md flex items-center justify-center border border-primary/30">
                    <PieChart className="h-8 w-8 text-primary" />
                  </div>
                  <div className="pl-8">
                    <h3 className="text-2xl font-bold mb-4 text-white">Strategic Diversification</h3>
                    <p className="text-white/70 text-lg">
                      We invest across multiple sectors and stages, providing balanced exposure to the innovation economy with risk-optimized portfolios.
                    </p>
                    <div className="mt-6 grid grid-cols-2 gap-4">
                      <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                        <div className="text-3xl font-bold text-primary">40%</div>
                        <div className="text-white/70 text-sm">Technology</div>
                      </div>
                      <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                        <div className="text-3xl font-bold text-primary">25%</div>
                        <div className="text-white/70 text-sm">Healthcare</div>
                      </div>
                      <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                        <div className="text-3xl font-bold text-primary">20%</div>
                        <div className="text-white/70 text-sm">Sustainability</div>
                      </div>
                      <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                        <div className="text-3xl font-bold text-primary">15%</div>
                        <div className="text-white/70 text-sm">Consumer</div>
                      </div>
                    </div>
                  </div>
                </GlassCard>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white">Portfolio Highlights</h3>
                  <div className="bg-primary/20 px-3 py-1 rounded-full text-primary text-sm font-medium">
                    Top Performers
                  </div>
                </div>

                <div className="space-y-6">
                  {portfolioHighlights.map((company, index) => (
                    <motion.div
                      key={company.name}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.1 * index }}
                      viewport={{ once: true }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10 hover:border-primary/30 transition-all duration-300"
                    >
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="text-xl font-bold text-white">{company.name}</h4>
                        <span className="text-primary font-semibold">{company.return}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-white/60">{company.industry}</span>
                        <span className="text-white/60">{company.investment} ({company.year})</span>
                      </div>
                      <div className="mt-3 flex items-center">
                        <div className={`px-2 py-0.5 rounded text-xs font-medium ${
                          company.status === 'Acquired'
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-blue-500/20 text-blue-400'
                        }`}>
                          {company.status}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <div className="mt-6 pt-4 border-t border-white/10 flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-primary" />
                    <span className="text-white/70">35+ Active Investments</span>
                  </div>
                  <Button variant="link" className="text-primary p-0" asChild>
                    <Link href="/investors/portfolio">
                      View Full Portfolio
                      <ArrowRight className="ml-1 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </GlassCard>
            </motion.div>
          </div>
        </div>
      </section>



      {/* Partners Section with Horizontal Scroll */}
      <section className="py-24 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-background to-black z-0" />
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[length:50px_50px]" />

        <div className="container mx-auto px-4 relative z-10">
          <TextReveal className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80">
              Our Strategic Partners
            </h2>
            <p className="text-xl text-white/70">
              We collaborate with leading organizations to provide comprehensive support for our portfolio companies.
            </p>
          </TextReveal>

          <HorizontalScroll
            containerClassName="py-8"
            className="gap-8 px-4"
            speed={0.3}
          >
            {partners.map((partner, index) => (
              <HorizontalScrollItem key={partner.id} width="w-80">
                <PartnerCard
                  name={partner.name}
                  logo={partner.logo}
                  description={partner.description}
                  partnershipType={partner.partnershipType}
                  website={partner.website}
                  index={index}
                />
              </HorizontalScrollItem>
            ))}
          </HorizontalScroll>
        </div>
      </section>

      {/* Contact Section */}
      <ContactSection />

      {/* Stats Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-background to-black z-0" />

        {/* Light rays effect */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1/2 h-full bg-primary/5 blur-3xl transform rotate-12 origin-top" />
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1/3 h-full bg-primary/5 blur-3xl transform -rotate-12 origin-top" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
                Our Impact in Numbers
              </h2>
              <p className="text-xl text-white/70 mb-10 max-w-2xl mx-auto">
                Join our community of visionary investors and be part of the next generation of groundbreaking innovations.
              </p>

              <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-8">
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="text-4xl font-bold text-primary mb-2">$120M+</div>
                  <div className="text-white/60">Capital Deployed</div>
                </motion.div>

                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <div className="text-4xl font-bold text-primary mb-2">45+</div>
                  <div className="text-white/60">Portfolio Companies</div>
                </motion.div>

                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <div className="text-4xl font-bold text-primary mb-2">4.7x</div>
                  <div className="text-white/60">Average Return</div>
                </motion.div>

                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <div className="text-4xl font-bold text-primary mb-2">12+</div>
                  <div className="text-white/60">Successful Exits</div>
                </motion.div>
              </div>

              <div className="mt-16 flex flex-wrap gap-4 justify-center">
                <Button size="lg" className="bg-primary/90 hover:bg-primary text-white backdrop-blur-sm px-8" asChild>
                  <Link href="/investors/join">
                    Become an Investor
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white/20 text-white hover:bg-white/10 backdrop-blur-sm px-8" asChild>
                  <Link href="/investors/contact">
                    Schedule a Consultation
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
}
