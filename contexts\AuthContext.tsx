'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { authService, type AuthUser } from '@/lib/supabase/auth';
import { createSupabaseClient } from '@/lib/supabase/client';

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, name: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  updateProfile: (data: Partial<AuthUser>) => Promise<{ success: boolean; error?: string }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = createSupabaseClient();

  useEffect(() => {
    // Get initial session
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        await checkAuth();
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAuth = async () => {
    try {
      const result = await authService.getCurrentUser();

      if (result.user) {
        setUser(result.user);

        // Set cookies for middleware compatibility if user is authenticated
        document.cookie = `innohub_auth=true; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days

        // Check if user is onboarded and set cookie accordingly
        if (result.user.name && result.user.email) {
          document.cookie = `userOnboarded=true; path=/; max-age=${60 * 60 * 24 * 7}`;
        }
      } else {
        setUser(null);
        // Clear cookies if no user
        document.cookie = 'innohub_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        document.cookie = 'userOnboarded=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setUser(null);
      // Clear cookies on error
      document.cookie = 'innohub_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      document.cookie = 'userOnboarded=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const result = await authService.signIn({ email, password });

      if (result.success && result.user) {
        setUser(result.user);

        // Set cookies for middleware compatibility
        document.cookie = `innohub_auth=true; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days

        // Check if user is onboarded and set cookie accordingly
        // For now, we'll assume users are onboarded if they have a complete profile
        if (result.user.name && result.user.email) {
          document.cookie = `userOnboarded=true; path=/; max-age=${60 * 60 * 24 * 7}`;
        }

        return { success: true };
      } else {
        return { success: false, error: result.error || 'Sign in failed' };
      }
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  };

  const signUp = async (email: string, password: string, name: string) => {
    try {
      const result = await authService.signUp({ email, password, name });

      if (result.success && result.user) {
        setUser(result.user);

        // Set cookies for middleware compatibility
        document.cookie = `innohub_auth=true; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days

        // New users typically need onboarding, but if they have name and email, consider them onboarded
        if (result.user.name && result.user.email) {
          document.cookie = `userOnboarded=true; path=/; max-age=${60 * 60 * 24 * 7}`;
        }

        return { success: true };
      } else {
        return { success: false, error: result.error || 'Sign up failed' };
      }
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  };

  const signOut = async () => {
    try {
      await authService.signOut();
      setUser(null);

      // Clear cookies
      document.cookie = 'innohub_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      document.cookie = 'userOnboarded=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  };

  const updateProfile = async (data: Partial<AuthUser>) => {
    try {
      if (!user) return { success: false, error: 'No user logged in' };

      const result = await authService.updateProfile(user.id, data);

      if (result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        return { success: false, error: result.error || 'Update failed' };
      }
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      signIn,
      signUp,
      signOut,
      updateProfile
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
