import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function POST(request: NextRequest) {
  try {
    const { email = '<EMAIL>', password = 'password123', name = 'Test User' } = await request.json();

    console.log('Creating test user:', { email, name });

    const serverClient = createSupabaseServerClient();

    // Create auth user
    const { data: authData, error: authError } = await serverClient.auth.admin.createUser({
      email,
      password,
      user_metadata: {
        name
      },
      email_confirm: true
    });

    if (authError) {
      console.error('Auth creation error:', authError);
      return NextResponse.json(
        { success: false, error: authError.message },
        { status: 400 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { success: false, error: 'Failed to create auth user' },
        { status: 500 }
      );
    }

    console.log('Auth user created:', authData.user.id);

    // Create user profile in database
    const { data: userData, error: userError } = await serverClient
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        name,
        role: 'STUDENT',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (userError) {
      console.error('User profile creation error:', userError);
      // Don't fail if profile creation fails
    }

    console.log('Test user created successfully');

    return NextResponse.json({
      success: true,
      user: {
        id: authData.user.id,
        email,
        name,
        role: 'STUDENT'
      }
    });

  } catch (error: any) {
    console.error('Test user creation failed:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}
