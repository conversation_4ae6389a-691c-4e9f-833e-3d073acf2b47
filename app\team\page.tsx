'use client';

import { motion } from 'framer-motion';
import { AnimatedText } from '@/components/ui/animated-text';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { TeamMemberCard } from '@/components/ui/team-member-card';
import { useLanguage } from '@/lib/context/language-context';



export default function TeamPage() {
  const { t } = useLanguage();

  const teamMembers = [
    {
      name: t('teamMember.alexJohnson.name'),
      role: t('teamMember.alexJohnson.role'),
      image: '/images/Team/team1.jpg',
      bio: t('teamMember.alexJohnson.bio'),
      expertise: ['Machine Learning', 'Neural Networks', 'Reinforcement Learning'],
    },
    {
      name: t('teamMember.sarahChen.name'),
      role: t('teamMember.sarahChen.role'),
      image: '/images/Team/team2.jpg',
      bio: t('teamMember.sarahChen.bio'),
      expertise: ['TensorFlow', 'PyTorch', 'MLOps'],
    },
    {
      name: t('teamMember.michaelRodriguez.name'),
      role: t('teamMember.michaelRodriguez.role'),
      image: '/images/Team/team3.jpg',
      bio: t('teamMember.michaelRodriguez.bio'),
      expertise: ['Statistical Analysis', 'Data Visualization', 'Predictive Modeling'],
    },
    {
      name: t('teamMember.emmaWilson.name'),
      role: t('teamMember.emmaWilson.role'),
      image: '/images/Team/team4.jpg',
      bio: t('teamMember.emmaWilson.bio'),
      expertise: ['CI/CD Pipelines', 'Kubernetes', 'Docker'],
    },
    {
      name: t('teamMember.davidKim.name'),
      role: t('teamMember.davidKim.role'),
      image: '/images/Team/team5.jpg',
      bio: t('teamMember.davidKim.bio'),
      expertise: ['BERT', 'GPT', 'Transformer Models'],
    },
    {
      name: t('teamMember.oliviaMartinez.name'),
      role: t('teamMember.oliviaMartinez.role'),
      image: '/images/Team/team6.jpg',
      bio: t('teamMember.oliviaMartinez.bio'),
      expertise: ['OpenCV', 'Image Processing', 'Object Detection'],
    },
    {
      name: t('teamMember.jamesWilson.name'),
      role: t('teamMember.jamesWilson.role'),
      image: '/images/Team/team7.jpg',
      bio: t('teamMember.jamesWilson.bio'),
      expertise: ['System Design', 'Cloud Architecture', 'Solution Engineering'],
    },
    {
      name: t('teamMember.sophiaLee.name'),
      role: t('teamMember.sophiaLee.role'),
      image: '/images/Team/team1.jpg',
      bio: t('teamMember.sophiaLee.bio'),
      expertise: ['Ethical AI', 'Bias Mitigation', 'AI Governance'],
    },
  ];

  return (
    <>
      <AnimatedGradientBackground className="min-h-[40vh] flex flex-col items-center justify-center pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-4"
            >
              {t('team.ourTeamBadge')}
            </motion.div>

            <AnimatedText
              text={t('team.meetOurExperts')}
              className="text-4xl md:text-5xl font-bold tracking-tight mb-6"
            />

            <AnimatedText
              text={t('team.collectiveDescription')}
              className="text-xl text-muted-foreground"
              once
            />
          </div>
        </div>
      </AnimatedGradientBackground>

      <section className="py-20 bg-black">
        <div className="w-full px-4">
          <TextReveal className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold mb-4 text-white">
              {t('team.mindsInnovation')}
            </h2>
            <p className="text-muted-foreground">
              {t('team.diverseTeamDescription')}
            </p>
          </TextReveal>

          {/* New Team Grid Layout - Full Width with optimizations */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mt-12 w-full px-2 will-change-transform">
            {teamMembers.map((member, index) => (
              <div
                key={`team-page-${member.name}`}
                className="will-change-transform"
                style={{
                  containIntrinsicSize: '0 360px', // Hint for browser about size
                  contentVisibility: 'auto' // Improve rendering performance
                }}
              >
                <TeamMemberCard
                  key={`team-card-${member.name}`}
                  name={member.name}
                  role={member.role}
                  image={member.image}
                  index={index}
                  className="h-[280px] md:h-[320px] lg:h-[360px]" // Reduced size by ~20%
                />
              </div>
            ))}
          </div>

          {/* Team Member Details Section */}
          <div className="mt-20">
            <TextReveal className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="text-2xl font-bold mb-4 text-white">
                {t('team.teamExpertise')}
              </h2>
              <p className="text-muted-foreground">
                {t('team.skillsExperience')}
              </p>
            </TextReveal>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {teamMembers.map((member) => (
                <HoverCard key={member.name}>
                  <HoverCardTrigger asChild>
                    <div className="p-4 border border-white/10 rounded-lg bg-black/50 backdrop-blur-sm cursor-pointer hover:border-primary/30 transition-all duration-300">
                      <h3 className="font-medium text-lg text-white">{member.name}</h3>
                      <p className="text-sm text-primary">{member.role}</p>
                    </div>
                  </HoverCardTrigger>
                  <HoverCardContent className="w-80 bg-black/80 backdrop-blur-xl border-white/10">
                    <div className="space-y-4">
                      <div className="flex justify-between space-x-4">
                        <div className="relative h-12 w-12 rounded-full bg-primary/20">
                          <div className="absolute inset-0 flex items-center justify-center text-primary">
                            {member.name.charAt(0)}
                          </div>
                        </div>
                        <div className="space-y-1">
                          <h4 className="text-sm font-semibold text-white">{member.name}</h4>
                          <p className="text-sm text-primary">{member.role}</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm text-white/80">{member.bio}</p>
                        <div className="flex flex-wrap gap-1">
                          {member.expertise.map((skill) => (
                            <span
                              key={skill}
                              className="inline-flex items-center rounded-full border px-2 py-0.5 text-xs font-semibold transition-colors border-transparent bg-primary/10 text-primary"
                            >
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>
              ))}
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
