import type { Metadata } from 'next';

// Define metadata for different languages
const metadataTranslations = {
  en: {
    home: {
      title: 'InnoHub - Innovation Hub for Startups',
      description: 'Accelerating the future of innovation with cutting-edge resources and mentorship for startups and entrepreneurs.',
    },
    about: {
      title: 'About Us - InnoHub',
      description: 'Learn about InnoHub\'s mission to foster innovation and support entrepreneurs in building successful businesses.',
    },
    team: {
      title: 'Our Team - InnoHub',
      description: 'Meet our team of AI experts and innovation specialists dedicated to helping startups succeed.',
    },
    investors: {
      title: 'Investors - InnoHub',
      description: 'Discover investment opportunities and partner with us to shape the future of innovation.',
    },
    programs: {
      title: 'Programs - InnoHub',
      description: 'Explore our comprehensive programs designed to accelerate startup growth and innovation.',
    },
    news: {
      title: 'News & Updates - InnoHub',
      description: 'Stay updated with the latest news, success stories, and developments from InnoHub.',
    },
    contact: {
      title: 'Contact Us - InnoHub',
      description: 'Get in touch with our team to discuss how we can help accelerate your startup journey.',
    },
    auth: {
      login: {
        title: 'Login - InnoH<PERSON>',
        description: 'Sign in to your InnoHub account to access courses and resources.',
      },
      register: {
        title: 'Register - InnoHub',
        description: 'Create your InnoHub account and start your innovation journey today.',
      },
    },
  },
  mn: {
    home: {
      title: 'ИнноХаб - Шинэлэг бизнесийн төв',
      description: 'Шинэлэг технологи, зөвлөгөө үзүүлэх үйлчилгээгээр бизнесийн ирээдүйг хурдасгах.',
    },
    about: {
      title: 'Бидний тухай - ИнноХаб',
      description: 'ИнноХабын эрхэм зорилго, шинэлэг бизнес эрхлэгчдийг дэмжих үйл ажиллагааны талаар мэдээлэл авах.',
    },
    team: {
      title: 'Манай баг - ИнноХаб',
      description: 'Хиймэл оюун ухаан, шинэлэг технологийн чиглэлээр ажилладаг манай багийн гишүүдтэй танилцах.',
    },
    investors: {
      title: 'Хөрөнгө оруулагчид - ИнноХаб',
      description: 'Хөрөнгө оруулалтын боломжууд, шинэлэг бизнесийн ирээдүйг хамтдаа бүтээх.',
    },
    programs: {
      title: 'Хөтөлбөрүүд - ИнноХаб',
      description: 'Шинэ бизнесийн өсөлт, шинэлэг технологийг дэмжих иж бүрэн хөтөлбөрүүдтэй танилцах.',
    },
    news: {
      title: 'Мэдээ, мэдээлэл - ИнноХаб',
      description: 'ИнноХабын сүүлийн үеийн мэдээ, амжилтын түүх, хөгжлийн талаарх мэдээллүүд.',
    },
    contact: {
      title: 'Холбоо барих - ИнноХаб',
      description: 'Манай багтай холбогдож, таны бизнесийн аялалыг хэрхэн дэмжих талаар ярилцах.',
    },
    auth: {
      login: {
        title: 'Нэвтрэх - ИнноХаб',
        description: 'ИнноХабын хичээл, нөөцөд хандахын тулд өөрийн бүртгэлээр нэвтрэх.',
      },
      register: {
        title: 'Бүртгүүлэх - ИнноХаб',
        description: 'ИнноХабын бүртгэл үүсгэж, шинэлэг технологийн аялалаа эхлүүлэх.',
      },
    },
  },
};

// Helper function to generate metadata for a specific page and language
export function generatePageMetadata(
  page: keyof typeof metadataTranslations.en,
  subpage?: string,
  language: 'en' | 'mn' = 'en'
): Metadata {
  const translations = metadataTranslations[language];
  
  let pageData;
  if (subpage && page === 'auth') {
    pageData = translations.auth[subpage as keyof typeof translations.auth];
  } else {
    pageData = translations[page];
  }

  if (!pageData) {
    // Fallback to English if translation not found
    const fallbackTranslations = metadataTranslations.en;
    if (subpage && page === 'auth') {
      pageData = fallbackTranslations.auth[subpage as keyof typeof fallbackTranslations.auth];
    } else {
      pageData = fallbackTranslations[page];
    }
  }

  return {
    title: pageData?.title || 'InnoHub',
    description: pageData?.description || 'Innovation Hub for Startups',
    openGraph: {
      title: pageData?.title || 'InnoHub',
      description: pageData?.description || 'Innovation Hub for Startups',
      type: 'website',
      locale: language === 'mn' ? 'mn_MN' : 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title: pageData?.title || 'InnoHub',
      description: pageData?.description || 'Innovation Hub for Startups',
    },
  };
}

// Default metadata for the root layout
export const defaultMetadata: Metadata = {
  title: 'InnoHub - Innovation Hub for Startups',
  description: 'Accelerating the future of innovation with cutting-edge resources and mentorship',
  keywords: ['innovation', 'startups', 'entrepreneurship', 'technology', 'mentorship', 'business'],
  authors: [{ name: 'InnoHub Team' }],
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://innohub.com',
    siteName: 'InnoHub',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@innohub',
  },
  robots: {
    index: true,
    follow: true,
  },
};
